-- Final City HUD - Client Main Script
local ESX = nil
local hudVisible = true
local playerData = {}

-- ESX Initialization (falls verwendet)
if GetResourceState('es_extended') == 'started' then
    ESX = exports["es_extended"]:getSharedObject()
end

-- HUD Data
local hudData = {
    health = 100,
    armor = 0,
    hunger = 100,
    thirst = 100,
    cash = 0,
    bank = 0,
    blackMoney = 0,
    job = "Arbeitslos",
    jobGrade = "Sozialhilfe",
    playerId = GetPlayerServerId(PlayerId()),
    location = {
        street = "Unbekannt",
        area = "Unbekannt"
    },
    time = {
        hour = 12,
        minute = 0
    },
    date = {
        day = 1,
        month = 1,
        year = 2024
    },
    voice = {
        talking = false,
        range = 100
    },
    radio = {
        active = false,
        talking = false
    }
}

-- Initialize HUD
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- NUI Focus deaktivieren für HUD
        SetNuiFocus(false, false)
        
        if hudVisible then
            -- Update HUD Data
            UpdateHudData()
            
            -- Send data to NUI
            SendNUIMessage({
                type = "updateHUD",
                data = hudData
            })
        end
        
        Citizen.Wait(1000) -- Update every second
    end
end)

-- Update HUD Data Function
function UpdateHudData()
    local playerPed = PlayerPedId()
    
    -- Health & Armor
    hudData.health = GetEntityHealth(playerPed) - 100
    hudData.armor = GetPedArmour(playerPed)
    
    -- Player ID
    hudData.playerId = GetPlayerServerId(PlayerId())
    
    -- Location
    local coords = GetEntityCoords(playerPed)
    local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    local crossingName = GetStreetNameFromHashKey(crossingHash)
    
    hudData.location.street = streetName
    if crossingName and crossingName ~= "" then
        hudData.location.area = crossingName
    else
        hudData.location.area = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z))
    end
    
    -- Time & Date
    local hour = GetClockHours()
    local minute = GetClockMinutes()
    local day = GetClockDayOfMonth()
    local month = GetClockMonth()
    local year = GetClockYear()
    
    hudData.time.hour = hour
    hudData.time.minute = minute
    hudData.date.day = day
    hudData.date.month = month
    hudData.date.year = year
    
    -- Voice Detection (falls vorhanden)
    if NetworkIsPlayerTalking(PlayerId()) then
        hudData.voice.talking = true
    else
        hudData.voice.talking = false
    end
end

-- ESX Events (falls ESX verwendet wird)
if ESX then
    RegisterNetEvent('esx:playerLoaded')
    AddEventHandler('esx:playerLoaded', function(xPlayer)
        playerData = xPlayer
        hudData.job = xPlayer.job.label
        hudData.jobGrade = xPlayer.job.grade_label
    end)
    
    RegisterNetEvent('esx:setJob')
    AddEventHandler('esx:setJob', function(job)
        hudData.job = job.label
        hudData.jobGrade = job.grade_label
    end)
    
    -- Money Updates
    RegisterNetEvent('esx:setAccountMoney')
    AddEventHandler('esx:setAccountMoney', function(account)
        if account.name == 'money' then
            hudData.cash = account.money
        elseif account.name == 'bank' then
            hudData.bank = account.money
        elseif account.name == 'black_money' then
            hudData.blackMoney = account.money
        end
    end)
    
    -- Status Updates (Hunger/Thirst)
    RegisterNetEvent('esx_status:onTick')
    AddEventHandler('esx_status:onTick', function(data)
        for i = 1, #data do
            if data[i].name == 'hunger' then
                hudData.hunger = math.floor(data[i].percent)
            elseif data[i].name == 'thirst' then
                hudData.thirst = math.floor(data[i].percent)
            end
        end
    end)
end

-- Commands
RegisterCommand('hud', function()
    ToggleHUD()
end, false)

RegisterCommand('testhud', function()
    -- Test Notification
    TriggerEvent('final-hud:showNotification', {
        title = 'Test',
        message = 'Das ist eine Test-Nachricht!',
        type = 'info',
        duration = 5000
    })
end, false)

-- Functions
function ToggleHUD()
    hudVisible = not hudVisible
    SendNUIMessage({
        type = "toggleHUD",
        show = hudVisible
    })
end

function UpdateMoney(cash, bank, blackMoney)
    hudData.cash = cash or hudData.cash
    hudData.bank = bank or hudData.bank
    hudData.blackMoney = blackMoney or hudData.blackMoney
end

-- Exports
exports('ToggleHUD', ToggleHUD)
exports('UpdateMoney', UpdateMoney)

-- Key Bindings
RegisterKeyMapping('hud', 'Toggle HUD', 'keyboard', 'F2')
