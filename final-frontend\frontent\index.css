body {
  background-color: black;
}
@import "https://fonts.cdnfonts.com/css/bebas-neue";
@import "https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap";


.main__corleone-ffa-hud-container {
  font-family: var(--ff-bebas);
}
.main__corleone-ffa-hud-top-container {
  position: absolute;
  top: 13vh;
  right: 2vh;
  z-index: -15;
}
.main__corleone-ffa-hud-top-bg-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 8vh;
  height: 8vh;
  border-top: 0.1vh solid rgba(255, 255, 255, 0.45);
  border-right: 0.1vh solid rgba(255, 255, 255, 0.45);
}
.main__corleone-ffa-hud-top-bg-x-1 {
  position: absolute;
  top: -1vh;
  left: -0.95vh;
  color: #ff6767;
  text-shadow: 0vh 0vh 1.7vh #ff6767;
}
.main__corleone-ffa-hud-top-bg-x-2 {
  position: absolute;
  bottom: -1vh;
  right: -0.5vh;
  color: #ff6767;
  text-shadow: 0vh 0vh 1.7vh #ff6767;
}
.main__corleone-ffa-hud-top-notify-container {
  margin-top: 1.5vh;
  margin-right: 1.5vh;
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
}
.main__corleone-ffa-hud-top-notify-item {
  display: flex;
  align-items: center;
  gap: 1vh;
  padding: 0.5vh 1vh 0.5vh 1.5vh;
  background: linear-gradient(to left, #cb4f4f3f 0%, rgba(255, 255, 255, 0) 100%);
  border-width: 0.1vh;
  border-style: solid;
  border-image: linear-gradient(to left, #cb4f4f 0%, rgba(255, 255, 255, 0) 100%) 1;
  border-left: none;
}
.main__corleone-ffa-hud-top-notify-item-icon img {
  width: 1.5vh;
  margin-top: 0.25vh;
}
.main__corleone-ffa-hud-top-notify-item-name {
  font-family: var(--ff-bebas);
  font-size: 1.8vh;
  color: var(--color-white);
}
.main__corleone-ffa-hud-top-notify-item-stripe {
  width: 0.2vh;
  height: 2.5vh;
  background: #cb4f4f;
  box-shadow: 0 0 1.7vh #cb4f4f;
}
.main__corleone-ffa-hud-top-notify-item-name.murdered {
  color: #ff8282;
  text-shadow: 0vh 0vh 1.7vh #ff8282;
}
.main__corleone-ffa-hud-bottom-container {
  position: absolute;
  bottom: 2.3vh;
  right: 0vh;
  width: 45.4vh;
  height: 7.3vh;
  background: url(images/ffa/bg_1.png);
  background-size: cover;
  z-index: -15;
  transform: scale(0.8);
}
.main__corleone-ffa-hud-bottom-item-container {
  position: absolute;
  width: 10%;
  left: 3.5vh;
  bottom: 1vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.main__corleone-ffa-hud-bottom-item-kills {
  width: 10.9vh;
  display: flex;
  align-items: center;
  font-size: 1.8vh;
  color: var(--color-white);
  position: relative;
  gap: 2vh;
}
.main__corleone-ffa-hud-bottom-item-kills p:first-child {
  width: 5vh;
  padding-left: 0.75vh;
}
.main__corleone-ffa-hud-bottom-item-kills p:last-child {
  width: 3vh;
  text-align: center;
  margin-left: -0.8vh;
}
.main__corleone-ffa-hud-bottom-item-deaths {
  width: 10.9vh;
  display: flex;
  align-items: center;
  font-size: 1.8vh;
  color: var(--color-white);
  gap: 0.75vh;
  margin-left: 1vh;
}
.main__corleone-ffa-hud-bottom-item-deaths p:first-child {
  width: 5vh;
  padding-left: 0.75vh;
}
.main__corleone-ffa-hud-bottom-item-deaths p:last-child {
  width: 5vh;
  text-align: center;
}
.main__corleone-ffa-hud-bottom-item-kda {
  width: 10.9vh;
  display: flex;
  align-items: center;
  font-size: 1.8vh;
  color: var(--color-white);
}
.main__corleone-ffa-hud-bottom-item-kda p:first-child {
  width: 5vh;
  padding-left: 0.75vh;
}
.main__corleone-ffa-hud-bottom-item-kda p:last-child {
  width: 5vh;
  text-align: center;
  margin-left: -1vh;
}
.main__corleone-ffa-hud-bottom-item-exp {
  width: 10.9vh;
  display: flex;
  align-items: center;
  font-size: 1.8vh;
  color: var(--color-white);
  gap: 0vh;
}
.main__corleone-ffa-hud-bottom-item-exp p:first-child {
  width: 5vh;
  padding-left: 0.75vh;
}
.main__corleone-ffa-hud-bottom-item-exp p:last-child {
  width: 5vh;
  text-align: center;
  margin-left: -0.65vh;
}
.main__corleone-ffa-hud-bottom-header {
  position: absolute;
  top: 0vh;
  left: 50%;
  transform: translate(-50%);
  font-size: 1.8vh;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--color-white);
}

.main__corleone-gangwar-hud-sphere {
  position: absolute;
  top: -20vh;
  left: 50%;
  transform: translate(-50%);
  width: 50vh;
  height: 20vh;
  border-radius: 50%;
  background: #000;
  filter: blur(10vh);
  z-index: -1;
}
.main__corleone-gangwar-hud-wrapper {
  position: absolute;
  z-index: 1;
  top: 2vh;
  left: 50%;
  transform: translate(-50%);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3vh;
}
.main__corleone-gangwar-hud-left-container,
.main__corleone-gangwar-hud-right-container {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 1vh;
}
.main__corleone-gangwar-hud-right-container {
  align-items: flex-end !important;
}
.main__corleone-gangwar-hud-header-container {
  display: flex;
  align-items: center;
  text-transform: uppercase;
  gap: 1vh;
  font-size: 1.5vh;
}
.main__corleone-gangwar-hud-middle-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.main__corleone-gangwar-hud-middle-img {
  position: relative;
  width: 5vh;
  height: 5vh;
}
.main__corleone-gangwar-hud-middle-img img {
  width: 100%;
}
.main__corleone-gangwar-hudd-middle-img-blur {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.main__corleone-gangwar-hudd-middle-img-blur img {
  width: 100%;
  filter: blur(1vh);
}
.main__corleone-gangwar-hud-middle-text {
  text-align: center;
}
.main__corleone-gangwar-hud-middle-text p:first-child {
  font-size: 1.5vh;
  font-family: var(--ff-gangwar);
}
.main__corleone-gangwar-hud-middle-text p:last-child {
  font-size: 2vh;
  font-family: var(--ff-gangwar);
  color: #e6a634;
  text-shadow: 0vh 0vh 1.7vh #e6a634;
}
.main__corleone-gangwar-hud-progress-bar {
  width: 30vh;
  background: rgba(0, 0, 0, 0.5);
  height: 1vh;
  transform: skew(-15deg);
}
.main__corleone-gangwar-hud-progress-bar-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--ff-gangwar-2);
  font-size: 1.2vh;
  margin-top: 0.5vh;
}
.main__corleone-gangwar-hud-progress-fill {
  width: 50%;
  height: 100%;
  background: #0274ed;
  box-shadow: 0 0 1.7vh #0274ed;
}
.main__corleone-gangwar-hud-progress-bar-2 {
  width: 30vh;
  background: rgba(0, 0, 0, 0.5);
  height: 1vh;
  transform: skew(15deg);
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.main__corleone-gangwar-hud-progress-fill-2 {
  width: 50%;
  height: 100%;
  background: var(--clr-pink);
  box-shadow: 0 0 1.7vh var(--clr-pink);
}

@keyframes hud-progress {
  0% {
    width: 0%;
  }
  to {
    width: 100%;
  }
}
.main__corleone-hud-container {
  font-size: 1.5vh;
  font-weight: 600;
  font-family: var(--ff-rad);
  color: var(--clr-white);
  overflow: hidden;
}
.main__genisis-hud-announce-item {
  width: 100%;
  padding: 0.5vh;
  border: 0.1vh solid #367dff;
  box-shadow: 0 0 5vh #367dff inset;
  border-radius: 0.25vh;
  font-size: 1.5vh;
  text-align: center;
  margin-top: 1vh;
}
.main__genisis-hud-announce-container {
  position: absolute;
  top: 2vh;
  left: 50%;
  transform: translate(-50%);
  width: 40vh;
  display: flex;
  flex-direction: column;
  gap: 2vh;
}
.main__genisis-hud-announce-header {
  width: 5vh;
  height: 2.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background: #367dff;
  box-shadow: 0 0 1.7vh #367dff;
  border-radius: 0.25vh;
  margin-top: -2vh;
  position: relative;
  left: 50%;
  transform: translate(-50%);
}
.main__genisis-hud-announce-text p:first-child {
  font-size: 1.25vh;
  color: #fff;
  text-transform: uppercase;
  font-family: var(--ff-header);
  margin-top: 0.5vh;
  font-weight: 700;
  color: #367dff;
}
.main__genisis-hud-announce-text p:last-child {
  font-size: 1.25vh;
  color: #fff;
}
.main__genisis-hud-announce-progress {
  width: 100%;
  height: 0.5vh;
  transform: skew(-10deg);
  background: rgba(255, 255, 255, 0.15);
  margin-top: 0.5vh;
}
.main__genisis-hud-announce-progress-inner {
  width: 70%;
  height: 100%;
  background: #367dff;
  box-shadow: 0 0 1.7vh #367dff;
}
.main__final-hud-notify-container {
  position: absolute;
  top: 15vh;
  left: 2vh;
  width: 25vh;
  height: 55vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
  z-index: -100;
}
.main__final-hud-notify-item {
  position: relative;
  border-radius: 0.25vh;
  padding: 1vh 1vh 1vh 1.5vh;
  line-height: 1vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}
.main__final-hud-notify-item-flex {
  display: flex;
  align-items: flex-start;
  gap: 1vh;
  flex-basis: 100%;
}
.main__final-hud-notify-item-flex.announce {
  display: grid;
  grid-template-columns: 0.01fr 1fr;
  gap: 1vh;
}
.main__final-hud-notify-item-icon {
  width: 2.25vh;
  height: 2.25vh;
  border: 0.1vh solid var(--clr-new-orange);
  box-shadow: 0 0 5vh var(--clr-new-orange);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.25vh;
  color: #00000080;
  background: var(--clr-new-orange);
}
.main__final-hud-notify-item-info p:first-child {
  font-weight: 600;
  font-size: 1.3vh;
  margin-top: 0.2vh;
}
.main__final-hud-notify-item-info.teamchat p:first-child {
  line-height: 1vh;
}
.main__final-hud-notify-item-info.teamchat p:last-child {
  word-break: break-all;
}
.main__final-hud-notify-item-info p:last-child {
  font-weight: 400;
  color: #ffffff80;
  font-size: 1.15vh;
  margin-top: 0.75vh;
  line-height: 1vh;
}
.main__final-hud-notify-progress {
  width: 100%;
  height: 0.15vh;
  background: rgba(255, 255, 255, 0.05);
  margin-top: 1vh;
}
.main__final-hud-notify-progress-fill {
  height: 100%;
  width: 100%;
  background: var(--clr-new-orange);
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
  border-radius: 0.5vh;
}
.main__final-hud-notify-item-icon.error {
  background: #ef2020;
  box-shadow: 0 0 5vh #ef2020;
  border: 0.1vh solid #ef2020;
}
.main__final-hud-notify-progress-fill.error {
  background: #ef2020;
  box-shadow: 0 0 1.7vh #ef2020;
}
.main__final-hud-notify-item-icon.success {
  background: #20ef41;
  box-shadow: 0 0 5vh #20ef41;
  border: 0.1vh solid #20ef41;
}
.main__final-hud-notify-item-icon.announce {
  background: var(--clr-new-blue);
  box-shadow: 0 0 5vh var(--clr-new-blue);
  border: 0.1vh solid var(--clr-new-blue);
}
.main__final-hud-notify-progress-fill.success {
  background: #20ef41;
  box-shadow: 0 0 1.7vh #20ef41;
}
.main__final-hud-notify-progress-fill.announce {
  background: var(--clr-new-blue);
  box-shadow: 0 0 1.7vh var(--clr-new-blue);
}
.main__final-hud-notify-stripe {
  position: absolute;
  left: -0.1vh;
  top: 50%;
  transform: translateY(-50%);
  width: 0.3vh;
  height: 3vh;
  background: var(--clr-new-orange);
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
}
.main__final-hud-notify-stripe.error {
  background: #ef2020;
  box-shadow: 0 0 1.7vh #ef2020;
}
.main__final-hud-notify-stripe.success {
  background: #20ef41;
  box-shadow: 0 0 1.7vh #20ef41;
}
.main__final-hud-notify-stripe.announce {
  background: var(--clr-new-blue);
  box-shadow: 0 0 1.7vh var(--clr-new-blue);
}
.main__final-hud-notify-item-bg {
  position: relative;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  padding: 0.5vh;
  border-radius: 0.5vh;
}
.main__final-hud-announce-container {
  position: absolute;
  top: 2vh;
  left: 50%;
  transform: translate(-50%);
  width: 40vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
  height: 15vh;
  z-index: -100;
}
.main__final-hud-teamchat-container {
  position: absolute;
  right: 2vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
  width: 25vh;
  height: 50vh;
  top: 22.5vh;
  z-index: -100;
}
.main__final-hud-notify-item-info p span {
  color: #fff;
}
.main__v3-hud-top-header-stars {
  position: absolute;
  top: 2.4vh;
  right: 6vh;
  transform: scale(1.85);
}
.fa-solid.fa-star.active:before {
  color: #fff;
}
.main__final-hud-top-left-information-container {
  position: absolute;
  top: 4vh;
  left: 2vh;
  display: flex;
  flex-direction: column;
  gap: 0.5vh;
  z-index: -1;
}
.main__final-hud-top-left-information-item {
  display: flex;
  align-items: center;
  gap: 1vh;
  transform: skew(-7.5deg);
  text-shadow: 0.2vh 0.2vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-hud-top-left-information-item:first-child {
  margin-top: 0;
}
.main__final-hud-top-left-information-item-icon {
  color: #fff;
  text-shadow: 0.2vh 0.2vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-hud-top-right-container {
  position: absolute;
  top: 3vh;
  right: 2vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
  align-items: flex-end;
}
.main__final__hud-top-right-logo {
  font-family: var(--ff-body);
  font-size: 2.5vh;
  color: var(--clr-new-orange);
  text-transform: uppercase;
  text-shadow: 0.25vh 0.25vh 0vh rgba(0, 0, 0, 0.5);
  font-weight: 800;
  transform: skew(-7.5deg);
  letter-spacing: 0.25vh;
}
.main__final__hud-top-right-logo p span {
  color: #fff;
  text-shadow: 0.25vh 0.25vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-hud-top-right-information-container {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__final__hud-top-right-information-bg {
  width: 1vh;
  height: 10vh;
  border-top: 0.1vh solid rgba(255, 255, 255, 0.25);
  border-right: 0.1vh solid rgba(255, 255, 255, 0.25);
  border-bottom: 0.1vh solid rgba(255, 255, 255, 0.25);
}
.main__final-hud-top-right-information-top-flex-item {
  position: relative;
  width: 2.5vh;
  height: 2.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.25);
  border-radius: 0.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff80;
  transform: skew(-7.5deg);
}
.main__corleone-hud-top-right-information-top-flex-item-id {
  height: 2.5vh;
  padding-left: 1vh;
  padding-right: 0.25vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.25);
  display: flex;
  align-items: center;
  gap: 1vh;
  border-radius: 0.5vh;
  transform: skew(-7.5deg);
  text-shadow: 0.25vh 0.25vh 0vh rgba(0, 0, 0, 0.5);
}
.main__corleone-hud-top-right-information-top-flex-item-id-icon {
  width: 2vh;
  height: 2vh;
  background: var(--clr-new-orange);
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
  border-radius: 0.25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-shadow: none;
}
.main__corleone-hud-top-right-information-top-flex-item-id-icon p {
  opacity: 0.75;
}
.main__final-hud-top-right-information-top-flex {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__final-hud-top-right-information-top-flex-item-range {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: -1vh;
  width: 2.5vh;
  height: 0.25vh;
  border-radius: 0.25vh;
  background: rgba(255, 255, 255, 0.25);
}
.main__final-hud-top-right-information-top-flex-item-range-fill {
  background: #56ff23;
  width: 50%;
  height: 100%;
  box-shadow: 0 0 1.7vh #56ff23;
}
.main__final-hud-top-right-information-top-flex-item.active {
  color: var(--clr-new-orange);
  text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
}
.main__final-hud-top-right-information-top-flex-item.speak {
  color: #56ff23;
  text-shadow: 0vh 0vh 1.7vh #56ff23;
}
.main__final-hud-top-right-information-top-flex-item.muted {
  color: #ff2323;
  text-shadow: 0vh 0vh 1.7vh #ff2323;
}
.main__final-hud-top-right-information-money {
  margin-top: 2vh;
  transform: skew(-7.5deg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1vh;
  text-shadow: 0.2vh 0.2vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-hud-top-right-information-black-money {
  transform: skew(-7.5deg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1vh;
  margin-top: 1vh;
  text-shadow: 0.2vh 0.2vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-hud-top-right-information-money-icon {
  width: 2.5vh;
  height: 2.5vh;
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.25);
  color: #fff;
  text-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.25vh;
  text-shadow: none;
}
.main__final-hud-top-right-information-ammonation {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0.25vh 0.25vh 0.25vh 1vh;
  border-radius: 0.5vh;
  gap: 1vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  margin-top: 1vh;
  transform: skew(-7.5deg);
  font-size: 1.35vh;
  margin-left: 3.6vh;
}
.main__corleone-hud-top-right-information-ammonation-count {
  text-align: right;
  font-size: 1.35vh;
  width: 4vh;
}
.main__corleone-hud-top-right-information-ammonation-icon {
  width: 2vh;
  height: 2vh;
  background: var(--clr-new-orange);
  border-radius: 0.25vh;
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
  display: flex;
  justify-content: center;
  align-items: center;
}
.main__corleone-hud-top-right-information-ammonation-icon img {
  width: 50%;
  opacity: 0.75;
}
.main__v3-hud-progess-container {
  position: absolute;
  left: 50%;
  bottom: 2vh;
  transform: translate(-50%);
  width: 25vh;
}
.main__v3-hud-progress-top-information-container {
  font-family: var(--ff-rad);
  font-size: 1.2vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main__v3-hud-progress-bottom-loading-container {
  width: 100%;
  height: 3vh;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.75) 0%, rgba(73, 0, 0, 0) 70.63%);
  margin-top: 0.5vh;
  transform: skew(-20deg);
  border-radius: 0.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.35vh;
  padding: 1vh;
}
.main__v3-hud-progress-bottom-loading-item {
  width: 0.75vh;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
}
.main__v3-hud-progress-bottom-loading-item.active {
  background: #a24e00;
  box-shadow: 0 0 1.7vh #a24900;
}
.main__v3-hud-interaction-container {
  position: absolute;
  left: 50%;
  transform: translate(-50%) scale(0.8);
  bottom: 2vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: -100;
}
.main__v3-hud-interaction-press-container {
  width: 10vh;
  height: 10vh;
  position: relative;
}
.main__v3-hud-interaction-press-icon-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}
.main__v3-hud-interaction-press-icon-container img {
  width: 12vh;
}
.main__v3-hud-interaction-press-text {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2vh;
  font-weight: 600;
  margin-top: -0.25vh;
}
.main__v3-hud-interaction-information-container {
  text-align: center;
  width: 15vh;
  font-family: var(--ff-inter);
  font-size: 1.2vh;
  font-weight: 400;
}
.main__v3-hud-interaction-press-text {
  color: #d99439;
  text-shadow: 0vh 0vh 1.7vh #d99439;
}
.main__v3-hud-interaction-press-text.active {
  color: #b0fa36;
  text-shadow: 0vh 0vh 1.7vh #b0fa36;
}
.main__v3-hud-interaction-press-text.notspeak {
  color: #ff4545;
  text-shadow: 0vh 0vh 1.7vh #ff4545;
}
.main__final-chat-container {
  position: absolute;
  left: 2vh;
  top: 10vh;
  height: 3.5vh;
  border: 0.1vh solid rgba(0, 0, 0, 0.5);
  background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.15));
  width: 27.5vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  padding-left: 1vh;
  padding-right: 1vh;
  border-radius: 0.5vh;
  z-index: -1;
}
.main__final-chat-input input {
  background: none;
  font-family: var(--ff-body);
  font-size: 1.5vh;
  color: #fff;
  outline: none;
  border: none;
  padding-bottom: 0.25vh;
  border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05);
  width: 22.5vh;
}
.main__final-chat-input input::placeholder {
  color: #ffffff80;
}
.main__final-chat-icon {
  color: #fff;
  text-shadow: 0vh 0vh 1.7vh #fff;
}
.main__corleone-hud-bottom-information-food-5-item {
  display: flex;
  align-items: center;
  gap: 0.75vh;
}
.main__corleone-hud-bottom-information-food-5-item-icon {
  width: 2.35vh;
  height: 2.35vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5vh;
  font-size: 1.25vh;
  align-items: center;
  transform: skew(-10deg);
  margin-top: 3vh;
  display: flex;
  justify-content: center;
}
.main__corleone-hud-bottom-information-food-5-item-percent {
  position: relative;
  display: flex;
  justify-content: flex-end;
  font-size: 1.25vh;
  color: #fff;
  text-shadow: 0vh 0vh 1.7vh #ffffff;
  margin-top: 0.3vh;
  margin-right: -0.75vh;
}
.main__corleone-hud-bottom-information-food-5-item-percent-item-container {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  transform: skew(-10deg);
  margin-top: 0.5vh;
}
.main__corleone-hud-bottom-information-food-5-item-percent-item {
  width: 0.8vh;
  height: 1.2vh;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 0.15vh;
}
.main__corleone-hud-bottom-information-food-5-item-content {
  margin-top: 1.15vh;
}
.main__corleone-hud-bottom-information-food-5-item-percent-item.active {
  background: #ffa725;
  box-shadow: 0 0 1.7vh #ffa725;
}
.main__corleone-hud-bottom-information-food-5-item-percent.drink5 {
  color: #fff;
  text-shadow: 0vh 0vh 1.7vh #ffffff;
}
.main__final-food-container {
  position: absolute;
  bottom: 20vh;
  left: 4vh;
  display: flex;
  align-items: center;
  gap: 4vh;
}
.blue,
.orange {
  color: #fff;
  text-shadow: 0vh 0vh 1.7vh #ffffff;
}
.green {
  color: #20ef41;
  text-shadow: 0vh 0vh 1.7vh #20ef41;
}
.red {
  color: #f34545;
  text-shadow: 0vh 0vh 1.7vh #f34545;
}
.main__corleone-hud-bottom-information-food-5-item-percent-item.active_drink5 {
  background: var(--clr-new-blue);
  box-shadow: 0 0 1.7vh var(--clr-new-blue);
}
.main__final-carhud-container {
  position: absolute;
  bottom: 4vh;
  right: 4vh;
  transform: skew(-7.5deg);
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__final-carhud-rpm {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  align-content: flex-end;
  justify-content: flex-end;
  gap: 0.5vh;
}
.main__final-carhud-rpm-item {
  width: 3vh;
  height: 1vh;
  background: rgba(255, 255, 255, 0.1);
}
.main__final-carhud-flex-top {
  display: flex;
  align-items: flex-end;
}
.main__final-carhud-flex-top-left {
  font-family: var(--ff-body);
  font-size: 5vh;
  font-weight: 700;
  color: #fff;
  text-shadow: 0vh 0vh 3vh rgba(0, 0, 0, 0.5);
  text-shadow: 0.25vh 0.5vh 0vh rgba(0, 0, 0, 0.5);
}
.main__final-carhud-flex-bottom {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__final-carhud-flex-bottom-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3vh;
  height: 3vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5vh;
  font-size: 1.25vh;
  color: #ffffff80;
}
.main__final-carhud-flex-bottom-item.active {
  color: var(--clr-new-orange);
  text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
}
.main__final-carhud-flex-bottom-item.active2 {
  color: #20ef41;
  text-shadow: 0vh 0vh 1.7vh #20ef41;
}
.main__final-carhud-flex-bottom-item.active3 {
  color: var(--clr-new-blue);
  text-shadow: 0vh 0vh 1.7vh var(--clr-new-blue);
}
.main__final-carhud-flex-bottom-item-stripe {
  position: absolute;
  bottom: -0.75vh;
  width: 2.5vh;
  height: 0.15vh;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5vh;
}
.main__final-carhud-flex-bottom-item-stripe.active {
  background: var(--clr-new-orange);
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
}
.main__final-carhud-flex-bottom-item-stripe.active2 {
  background: #20ef41;
  box-shadow: 0 0 1.7vh #20ef41;
}
.main__final-carhud-flex-bottom-item-stripe.active3 {
  background: var(--clr-new-blue);
  box-shadow: 0 0 1.7vh var(--clr-new-blue);
}
.main__final-carhud-flex-top-left p span {
  font-size: 1.5vh;
  font-weight: 500;
}
.main__final-carhud-fuel-container {
  display: flex;
  align-items: center;
  gap: 1vh;
  margin-bottom: 1vh;
}
.main__final-carhud-fuel-icon {
  height: 3vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 1vh;
  padding-right: 1vh;
  gap: 1vh;
  border-radius: 0.5vh;
}
.main__final-carhud-fuel-icon p {
  color: #ffffff80;
}
.main__final-carhud-fuel-progress {
  width: 20vh;
  height: 0.25vh;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 5vh;
}
.main__final-carhud-fuel-progress-fill {
  width: 50%;
  height: 100%;
  background: #20ef41;
  box-shadow: 0 0 1.7vh #20ef41;
  border-radius: 5vh;
}
.main__final-carhud-rpm-item.width-7,
.main__final-carhud-rpm-item.width-6,
.main__final-carhud-rpm-item.width-5,
.main__final-carhud-rpm-item.width-4,
.main__final-carhud-rpm-item.width-3,
.main__final-carhud-rpm-item.width-2,
.main__final-carhud-rpm-item.width-1 {
  width: 2vh;
}
.main__final-carhud-rpm-item.active {
  background: var(--clr-new-orange);
  box-shadow: 0 0 1.7vh var(--clr-new-orange);
}
.main__final-carhud-flex-top-right-gear {
  position: absolute;
  right: 0;
  bottom: 5vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  height: 3vh;
  padding-left: 1vh;
  padding-right: 1vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5vh;
}
.main__final-carhud-flex-top-right-gear-icon {
  color: var(--clr-new-blue);
  text-shadow: 0vh 0vh 1.7vh var(--clr-new-blue);
}
.main__corleone-hud-settings-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40vh;
  padding: 3vh;
  border-radius: 1vh;
  background: url(images/main_bg/bg_2.png);
  background-size: cover;
  background-position: center;
}
.main__corleone-hud-settings-content-container {
  position: relative;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  padding: 1vh;
}
.main__corleone-hud-settings-content-header {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__corleone-hud-settings-content-header-img {
  width: 5vh;
  height: 5vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main__corleone-hud-settings-content-header-img img {
  width: 16vh;
  margin-top: 0.75vh;
}
.main__corleone-hud-settings-content-header-text p:first-child {
  font-family: var(--ff-agency);
  font-size: 2.2vh;
  text-transform: uppercase;
  color: var(--clr-orange);
  text-shadow: 0vh 0vh 1.7vh var(--clr-orange);
}
.main__corleone-hud-settings-content-header-text p:first-child span {
  color: var(--color-white);
  text-shadow: 0vh 0vh 1.7vh var(--color-white);
}
.main__corleone-hud-settings-content-header-text p:last-child {
  font-size: 1.35vh;
  color: #ffffff8c;
  margin-top: -0.25vh;
}
.main__corleone-hud-settings-content-img {
  position: absolute;
  top: -1.5vh;
  left: 50%;
  transform: translate(-50%);
  width: 20vh;
  -webkit-backdrop-filter: blur(0.5vh);
  backdrop-filter: blur(0.5vh);
  display: flex;
  justify-content: center;
  align-items: center;
}
.main__corleone-hud-settings-content-img img {
  width: 90%;
}
.main__corleone-hud-settings-item-container {
  margin-top: 2vh;
  height: 33.75vh;
  overflow-y: scroll;
  padding-right: 1vh;
  display: flex;
  flex-direction: column;
  gap: 1vh;
}
.main__corleone-hud-settings-item-container::-webkit-scrollbar {
  width: 0.3vh;
}
.main__corleone-hud-settings-item-container::-webkit-scrollbar-track {
  width: 0.3vh;
  background: rgba(255, 255, 255, 0.05);
}
.main__corleone-hud-settings-item-container::-webkit-scrollbar-thumb {
  width: 0.3vh;
  background: var(--clr-orange);
}
.main__corleone-hud-settings-item {
  width: 100%;
  padding: 1vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: url(/img/hud_2/settings_border.png);
  background-size: cover;
  background-position: center;
  border-radius: 0.25vh;
  border: 0.1vh solid #d996391c;
}
.main__corleone-hud-settings-left-container p:first-child {
  font-size: 1.5vh;
  color: var(--color-white);
}
.main__corleone-hud-settings-left-container p:last-child {
  font-size: 1.35vh;
  color: #ffffff8c;
}
.main__corleone-hud-settings-right-container {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}
.main__corleone-hud-settings-right-btn {
  width: 2.75vh;
  height: 2.75vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2vh;
  color: var(--color-white);
  cursor: pointer;
  transition: 0.2s ease-in;
  background: radial-gradient(var(--clr-orange), rgba(0, 0, 0, 0));
  border-radius: 0.25vh;
}
.main__corleone-hud-settings-right-btn.inactive {
  opacity: 0.5;
  cursor: not-allowed;
}
.main__corleone-hud-settings-right-btn:not(.inactive):hover {
  box-shadow: 0 0 1.7vh var(--clr-orange);
  background: radial-gradient(var(--clr-orange), var(--clr-orange));
}
.main__corleone-hud-settings-right-text-container {
  width: 5.5vh;
  height: 2.75vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 1.35vh;
  color: var(--color-white);
  background: radial-gradient(#d9a43938, rgba(0, 0, 0, 0));
}
.main__corleone-hud-settings-right-checkbox {
  width: 2.75vh;
  height: 2.75vh;
  background: radial-gradient(#d9a43938, rgba(0, 0, 0, 0));
  border: 0.1vh solid #d996391c;
  border-radius: 0.25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: 0.2s ease-in;
  font-size: 1.35vh;
  color: #ffbf76;
  text-shadow: 0vh 0vh 1.7vh #ffbf76;
}
.main__corleone-hud-settings-right-checkbox:hover {
  border: 0.1vh solid #d99c39be;
  box-shadow: 0 0 1.7vh #d99c39be;
}
.main__corleone-hud-settings-btn-container {
  margin-top: 1vh;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1vh;
}
.main__corleone-hud-settings-btn-save {
  width: 100%;
  height: 4.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: 0.2s ease-in;
  font-size: 1.35vh;
  color: var(--color-white);
  background: radial-gradient(rgba(0, 0, 0, 0) 0%, var(--clr-orange) 100%);
  border-radius: 0.25vh;
  border: 0.1vh solid var(--clr-orange);
}
.main__corleone-hud-settings-btn-save:hover {
  box-shadow: 0 0 1.7vh var(--clr-orange);
}
.main__corleone-hud-settings-btn-reset {
  width: 100%;
  height: 4.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: 0.2s ease-in;
  font-size: 1.35vh;
  color: var(--color-white);
  background: radial-gradient(#b6123b5b 20%, #ff275b 100%);
  border-radius: 0.25vh;
  border: 0.1vh solid #ff275b;
}
.main__corleone-hud-settings-btn-reset:hover,
.main__corleone-hud-settings-close-btn:hover {
  box-shadow: 0 0 1.7vh #ff275b;
}
.main__corleone-hud-settings-close-btn {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 2.25vh;
  right: 2vh;
  width: 3vh;
  height: 3vh;
  font-size: 1.35vh;
  color: var(--color-white);
  cursor: pointer;
  transition: 0.2s ease-in;
  background: radial-gradient(#b6123b5b 20%, #ff275b 100%);
  border-radius: 0.25vh;
  border: 0.1vh solid #ff275b;
}
.main__v3-hud-lifeinvader-container {
  position: absolute;
  bottom: 25vh;
  left: 2vh;
  width: 25vh;
}
.main__v3-hud-lifeinvader-item {
  width: 100%;
}
.main__v3-hud-lifeinvader-top-header {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__v3-hud-lifeinvader-header-container {
  width: 100%;
  padding: 0.5vh;
  background: linear-gradient(to right, rgba(254, 70, 70, 1), rgba(255, 255, 255, 0));
  border-radius: 1vh 1vh 0vh 0vh;
}
.main__v3-hud-lifeinvader-icon img {
  width: 3.25vh;
}
.main__v3-hud-lifeinvader-header-right {
  margin-top: 0.75vh;
}
.main__v3-hud-lifeinvader-header-big {
  font-size: 1.5vh;
  text-transform: uppercase;
  font-weight: 700;
  margin-top: -0.5vh;
}
.main__v3-hud-lifeinvader-header-small {
  font-size: 1vh;
  font-weight: 500;
  margin-top: -0.25vh;
  margin-bottom: 0.5vh;
}
.main__v3-hud-lifeinvader-bottom-header {
  width: 100%;
  background: linear-gradient(to right, #bf2a2a, rgba(255, 255, 255, 0));
  padding: 0.5vh;
  border-radius: 0.5vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main__v3-hud-lifeinvader-bottom-header-left {
  display: flex;
  align-items: center;
  gap: 1vh;
  font-size: 1vh;
}
.main__v3-hud-lifeinvader-bottom-header-right {
  font-size: 1vh;
}
.main__v3-hud-lifeinvader-bottom-header-left-icon {
  width: 2.5vh;
  height: 2.5vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 0.5vh;
  font-size: 1.2vh;
}
.main__v3-hud-lifeinvader-text-message-container {
  width: 100%;
  margin-top: 0.25vh;
  padding: 1vh;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.55), rgba(255, 255, 255, 0));
  border-radius: 0vh 0vh 1vh 1vh;
  border-top: 0.3vh solid rgba(254, 70, 70, 1);
  font-size: 1.35vh;
  font-weight: 400;
  color: #ffffff8c;
}
.main__corleone-hud-teamchat-container {
  position: absolute;
  bottom: 25vh;
  right: 2vh;
  width: 25vh;
  height: 41vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 1vh;
}
.main__corleone-hud-notify-flex-container.teamchat {
  flex-direction: row-reverse;
}
.main__corleone-hud-notify-top-content-progress.teamchat2 {
  transform: rotate(180deg);
}
.main__final-hud-location-container {
  position: absolute;
  top: 94vh;
  left: 30vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  width: 16vh;
  z-index: -1;
}
.main__final-hud-job-container {
  position: absolute;
  top: 90vh;
  left: 30vh;
  display: flex;
  align-items: center;
  gap: 1vh;
  z-index: -100;
  width: 12vh;
  z-index: -1;
}
.main__final-hud-bottom-information-item {
  display: flex;
  align-items: center;
  gap: 1vh;
  transform: skew(-7.5deg);
}
.main__final-hud-bottom-information-item-icon {
  width: 3vh;
  height: 3vh;
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
.main__final-hud-bottom-information-item-text {
  line-height: 0;
  font-size: 1.25vh;
}
.main__final-hud-bottom-information-item-text p:last-child {
  color: #ffffff80;
  margin-top: 1.5vh;
  font-size: 1.25vh;
}