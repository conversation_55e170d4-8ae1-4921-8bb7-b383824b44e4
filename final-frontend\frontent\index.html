<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Page Title</title>
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-thin.css" />
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-light.css" />
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-regular.css" />
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-solid.css" />
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-duotone-solid.css" />
  <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/all.css" />
  <link rel="stylesheet" data-purpose="Layout StyleSheet" title="Web Awesome"
    href="/css/app-wa-3b124ff0e0d7a67cd8c995d0aeb1d15a.css?vsn=d" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="stylesheet" type="text/css" media="screen" href="config.css" />
  <link rel="stylesheet" type="text/css" media="screen" href="index.css" />
  <link rel="stylesheet" type="text/css" media="screen" href="style.css" />
</head>
  <!-- HUD -->
  <div class="main__corleone-hud-container" style="display: blo;">
    <div class="main__final-hud-notify-container">
      <div class="main__final-hud-notify-item-bg notify" style="transform: translateX(0vh) translateZ(0px);">
        <div class="main__final-hud-notify-item">
          <div class="main__final-hud-notify-item-flex announce">
            <div class="main__final-hud-notify-item-icon info"><i class="fa-sharp fa-solid fa-circle-exclamation"
                aria-hidden="true"></i></div>
            <div class="main__final-hud-notify-item-info">
              <p>[Business Meldung: Casino]</p>
              <p>Kostenloser Eintritt.</p>
            </div>
          </div>
          <div class="main__final-hud-notify-stripe info"></div>
          <div class="main__final-hud-notify-progress">
            <div class="main__final-hud-notify-progress-fill info"
              style="animation: 7000ms linear 0s 1 normal none running hud-progress;"></div>
          </div>
        </div>
      </div>
      <div class="main__final-hud-notify-item-bg notify" style="transform: translateX(0vh) translateZ(0px);">
        <div class="main__final-hud-notify-item">
          <div class="main__final-hud-notify-item-flex announce">
            <div class="main__final-hud-notify-item-icon success"><i class="fa-sharp fa-solid fa-circle-check"
                aria-hidden="true"></i></div>
            <div class="main__final-hud-notify-item-info">
              <p>Banking</p>
              <p>Du hast 1$ von deinem Konto abgehoben.</p>
            </div>
          </div>
          <div class="main__final-hud-notify-stripe success"></div>
          <div class="main__final-hud-notify-progress">
            <div class="main__final-hud-notify-progress-fill success"
              style="animation: 4000ms linear 0s 1 normal none running hud-progress;"></div>
          </div>
        </div>
      </div>
      <div class="main__final-hud-notify-item-bg notify" style="transform: translateX(0vh) translateZ(0px);">
        <div class="main__final-hud-notify-item">
          <div class="main__final-hud-notify-item-flex announce">
            <div class="main__final-hud-notify-item-icon error"><i class="fa-sharp fa-solid fa-circle-xmark"
                aria-hidden="true"></i></div>
            <div class="main__final-hud-notify-item-info">
              <p>Information</p>
              <p>Fahrzeug Abgeschlossen!.</p>
            </div>
          </div>
          <div class="main__final-hud-notify-stripe error"></div>
          <div class="main__final-hud-notify-progress">
            <div class="main__final-hud-notify-progress-fill error"
              style="animation: 4000ms linear 0s 1 normal none running hud-progress;"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="main__final-hud-teamchat-container"></div>
    <div class="main__final-hud-announce-container">
      <div class="main__final-hud-notify-item-bg announce" style="transform: translateY(0vh) translateZ(0px);">
        <div class="main__final-hud-notify-item">
          <div class="main__final-hud-notify-item-flex announce">
            <div class="main__final-hud-notify-item-icon"><i class="fa-sharp fa-solid fa-circle-exclamation"
                aria-hidden="true"></i></div>
            <div class="main__final-hud-notify-item-info">
              <p>Fahrzeug Despawns</p>
              <p>Fahrzeuge, die mehr als 30 Meter von einem Spieler entfernt sind, werden in 60 Minuten despawned...</p>
            </div>
          </div>
          <div class="main__final-hud-notify-stripe"></div>
          <div class="main__final-hud-notify-progress">
            <div class="main__final-hud-notify-progress-fill"
              style="animation: 10000ms linear 0s 1 normal none running hud-progress;"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="main__final-hud-top-left-information-container">
      <div class="main__final-hud-top-left-information-item">
        <div class="main__final-hud-top-left-information-item-icon"><i class="fa-sharp fa-solid fa-clock"
            aria-hidden="true"></i></div>
        <p>21:49</p>
      </div>
      <div class="main__final-hud-top-left-information-item">
        <div class="main__final-hud-top-left-information-item-icon"><i class="fa-sharp fa-regular fa-calendar"
            aria-hidden="true"></i></div>
        <p>13.11.2024</p>
      </div>
    </div>
    <div class="main__final-hud-top-right-container">
      <div class="main__final__hud-top-right-logo">
        <p>Final<span>City</span></p>
      </div>
      <div class="main__final-hud-top-right-information-container">
        <div class="main__fional__hud-top-right-information-item">
          <div class="main__final-hud-top-right-information-top-flex">
            <div class="main__corleone-hud-top-right-information-top-flex-item-id">
              <p>1898</p>
              <div class="main__corleone-hud-top-right-information-top-flex-item-id-icon">
                <p>ID</p>
              </div>
            </div>
            <div class="main__final-hud-top-right-information-top-flex-item false"><i
                class="fa-sharp fa-solid fa-walkie-talkie" aria-hidden="true"></i></div>
            <div class="main__final-hud-top-right-information-top-flex-item false"><i
                class="fa-sharp fa-solid fa-microphone" aria-hidden="true"></i>
              <div class="main__final-hud-top-right-information-top-flex-item-range">
                <div class="main__final-hud-top-right-information-top-flex-item-range-fill" style="width: 100%;"></div>
              </div>
            </div>
          </div>
          <div class="main__v3-hud-kino-container-disable-shit">
            <div class="main__final-hud-top-right-information-money">
              <p>100€</p>
              <div class="main__final-hud-top-right-information-money-icon green"><i class="fa-sharp fa-solid fa-wallet"
                  aria-hidden="true"></i></div>
            </div>
            <div class="main__final-hud-top-right-information-black-money">
              <p>46.495€</p>
              <div class="main__final-hud-top-right-information-money-icon red"><i
                  class="fa-sharp fa-solid fa-building-columns" aria-hidden="true"></i></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="main__final-hud-job-container react-draggable" style="transform: translate(0px, 0px);">
      <div class="main__final-hud-bottom-information-item">
        <div class="main__final-hud-bottom-information-item-icon"><i class="fa-sharp fa-solid fa-user"
            aria-hidden="true"></i></div>
        <div class="main__final-hud-bottom-information-item-text">
          <p>Arbeitslos</p>
          <p>Sozialhilfe</p>
        </div>
      </div>
    </div>
    <div class="main__final-hud-location-container react-draggable" style="transform: translate(0px, 0px);">
      <div class="main__final-hud-bottom-information-item">
        <div class="main__final-hud-bottom-information-item-icon"><i class="fa-sharp fa-solid fa-location-crosshairs"
            aria-hidden="true"></i></div>
        <div class="main__final-hud-bottom-information-item-text">
          <p id="streetA">Unbekannt</p>
          <p id="streetB">Unbekannt 1000</p>
        </div>
      </div>
    </div>
    <div class="main__final-food-container">
      <div class="main__corleone-hud-bottom-information-food-5-item react-draggable"
        style="transform: translate(0px, 0px);">
        <div class="main__corleone-hud-bottom-information-food-5-item-icon blue"><i class="fa-sharp fa-solid fa-glass"
            aria-hidden="true"></i></div>
        <div class="main__corleone-hud-bottom-information-food-5-item-content">
          <div class="main__corleone-hud-bottom-information-food-5-item-percent drink5" id="thirstPercent">
            <p>40%</p>
          </div>
          <div class="main__corleone-hud-bottom-information-food-5-item-percent-item-container">
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item active_drink5"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item active_drink5"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
          </div>
        </div>
      </div>
      <div class="main__corleone-hud-bottom-information-food-5-item react-draggable"
        style="transform: translate(0px, 0px);">
        <div class="main__corleone-hud-bottom-information-food-5-item-icon orange"><i
            class="fa-sharp fa-solid fa-fork-knife" aria-hidden="true"></i></div>
        <div class="main__corleone-hud-bottom-information-food-5-item-content">
          <div class="main__corleone-hud-bottom-information-food-5-item-percent">
            <p>0%</p>
          </div>
          <div class="main__corleone-hud-bottom-information-food-5-item-percent-item-container">
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
            <div class="main__corleone-hud-bottom-information-food-5-item-percent-item false"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="main__corleone-hud-settings-container"
      style="opacity: 1; transform: translateX(-50%) translateY(-50%) scale(1) translateZ(0px);">
      <div class="main__corleone-hud-settings-content-container">
        <div class="main__corleone-hud-settings-content-header">
          <div class="main__corleone-hud-settings-content-header-img"><img src="img/hud/settings_icon.png" alt=""></div>
          <div class="main__corleone-hud-settings-content-header-text">
            <p>Settings<span>Panel</span></p>
            <p>Hier kannst du dein Hud Einstellen</p>
          </div>
        </div>
        <div class="main__corleone-hud-settings-item-container">
          <div class="main__corleone-hud-settings-item">
            <div class="main__corleone-hud-settings-left-container">
              <p>Job</p>
              <p>Willst du es an oder aus haben?</p>
            </div>
            <div class="main__corleone-hud-settings-right-container">
              <div class="main__corleone-hud-settings-right-checkbox">
                <div class="main__corleone-hud-settings-right-checkbox-inner"><i class="fa-regular fa-check"
                    aria-hidden="true"></i></div>
              </div>
            </div>
          </div>
          <div class="main__corleone-hud-settings-item">
            <div class="main__corleone-hud-settings-left-container">
              <p>Straße</p>
              <p>Willst du es an oder aus haben?</p>
            </div>
            <div class="main__corleone-hud-settings-right-container">
              <div class="main__corleone-hud-settings-right-checkbox">
                <div class="main__corleone-hud-settings-right-checkbox-inner"><i class="fa-regular fa-check"
                    aria-hidden="true"></i></div>
              </div>
            </div>
          </div>
          <div class="main__corleone-hud-settings-item">
            <div class="main__corleone-hud-settings-left-container">
              <p>Bank oder Schwarzgeld</p>
              <p>Willst du es an oder aus haben?</p>
            </div>
            <div class="main__corleone-hud-settings-right-container">
              <div class="main__corleone-hud-settings-right-checkbox">
                <div class="main__corleone-hud-settings-right-checkbox-inner"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="main__corleone-hud-settings-btn-container">
          <div class="main__corleone-hud-settings-btn-save">
            <p>Speichern</p>
          </div>
          <div class="main__corleone-hud-settings-btn-reset">
            <p>Zurücksetzen</p>
          </div>
        </div>
        <div class="main__corleone-hud-settings-close-btn"><i class="fa-light fa-xmark" aria-hidden="true"></i></div>
      </div>
    </div>
    <div class="main__final-chat-container" style="opacity: 1;">
      <div class="main__final-chat-icon"><i class="fa-sharp fa-solid fa-comment" aria-hidden="true"></i></div>
      <div class="main__final-chat-input"><input type="text" placeholder="Write a message here... " value=""></div>
    </div>
    <div class="main__v3-hud-interaction-container"
      style="transform: translateX(-50%) translateY(0vh) scale(0.8) translateZ(0px);">
      <div class="main__v3-hud-interaction-press-container">
        <div class="main__v3-hud-interaction-press-icon-container"><img src="img/hud/interaction_bg.png" alt=""></div>
        <div class="main__v3-hud-interaction-press-text">
          <p>E</p>
        </div>
      </div>
      <div class="main__v3-hud-interaction-information-container">
        <p>Drücke E um auf die Auto Garage zuzugreifen</p>
      </div>
    </div>
    <div class="main__v3-hud-lifeinvader-container"></div>
  </div>